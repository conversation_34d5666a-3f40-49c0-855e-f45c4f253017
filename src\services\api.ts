const API_BASE_URL = 'http://localhost:8000/api';

interface ApiResponse<T> {
  data?: T;
  error?: string;
}

class ApiService {
  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        throw new Error(`API Error: ${response.status} ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error);
      throw error;
    }
  }

  async get<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET' });
  }

  async post<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T>(endpoint: string, data: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }

  // Agent methods
  async getAgents() {
    const response = await this.get<{ agents: any[] }>('/agents');
    return response.agents;
  }

  async getAgent(agentId: string) {
    return this.get(`/agents/${agentId}`);
  }

  async updateAgent(agentId: string, agentData: any) {
    return this.put(`/agents/${agentId}`, agentData);
  }

  async createAgent(agentData: any) {
    return this.post('/agents', agentData);
  }

  // User methods
  async getUsers() {
    return this.get<any[]>('/users');
  }

  async getUser(userId: number) {
    return this.get(`/users/${userId}`);
  }

  async login(credentials: { email: string; userType: string; adminRole?: string }) {
    return this.post('/auth/login', credentials);
  }

  // Course methods
  async getCourses() {
    const response = await this.get<{ courses: any[] }>('/courses');
    return response.courses;
  }

  async getCourseContent(courseId: number) {
    return this.get(`/courses/${courseId}`);
  }

  async createCourse(courseData: any) {
    return this.post('/courses', courseData);
  }

  // Learner methods
  async getLearners() {
    const response = await this.get<{ learners: any[] }>('/learners');
    return response.learners;
  }

  async getLearner(learnerId: number) {
    return this.get(`/learners/${learnerId}`);
  }

  async assignCourse(learnerId: number, courseId: number) {
    return this.post(`/learners/${learnerId}/assign-course`, { courseId });
  }

  // Learning Goals methods
  async getLearningGoals() {
    const response = await this.get<{ goals: any[] }>('/learning-goals');
    return response.goals;
  }

  async createLearningGoal(goalData: any) {
    return this.post('/learning-goals', goalData);
  }

  // Analytics methods
  async getAnalytics() {
    return this.get('/analytics');
  }
}

// Create and export a singleton instance
const apiService = new ApiService();
export default apiService;

// Helper function for handling API calls with localStorage fallback
export const withFallback = async <T>(
  apiCall: () => Promise<T>,
  fallbackKey: string,
  defaultValue: T
): Promise<T> => {
  try {
    const result = await apiCall();
    // Optionally cache the result in localStorage
    localStorage.setItem(fallbackKey, JSON.stringify(result));
    return result;
  } catch (error) {
    console.error('API call failed, using localStorage fallback:', error);
    const fallbackData = localStorage.getItem(fallbackKey);
    return fallbackData ? JSON.parse(fallbackData) : defaultValue;
  }
};
